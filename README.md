# نظام نقاط البيع (POS)

نظام نقاط بيع بسيط وسهل الاستخدام للمحلات التجارية، مبني باستخدام Python و PyQt6.

## المميزات

- واجهة مستخدم سهلة وبسيطة
- دعم اللغة العربية بالكامل
- إدارة المنتجات والطلبات بسهولة
- حساب الفواتير تلقائياً
- دعم الفئات والبحث عن المنتجات

## متطلبات التشغيل

- Python 3.7 أو أحدث
- PyQt6
- qdarktheme

## طريقة التثبيت

1. قم بتثبيت المتطلبات باستخدام الأمر التالي:

```bash
pip install -r requirements.txt
```

## طريقة التشغيل

لتشغيل التطبيق، قم بتنفيذ الأمر التالي في سطر الأوامر:

```bash
python pos_app.py
```

## طريقة الاستخدام

1. ابحث عن المنتجات باستخدام حقل البحث أو اختر الفئة المطلوبة
2. انقر على المنتج لإضافته إلى السلة
3. يمكنك تعديل الكمية أو حذف العناصر من السلة
4. اضغط على زر "إتمام البيع" لإنهاء العملية

## التخصيص

يمكنك تعديل قائمة المنتجات في ملف `pos_app.py` في دالة `load_products` لإضافة أو تعديل المنتجات المتوفرة في النظام.

## الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE).
