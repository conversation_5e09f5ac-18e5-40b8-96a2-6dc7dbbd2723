import sys
import json
import os
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                             QHeaderView, QMessageBox, QComboBox, QDoubleSpinBox, QFrame)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QFont, QIcon, QPalette, QColor 

class POSApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام نقاط البيع")
        self.setMinimumSize(1000, 700)
        
        # Initialize data
        self.products = []
        self.cart = []
        self.load_products()
        
        # Set up the main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QHBoxLayout(main_widget)
        
        # Left side - Product list
        left_panel = QVBoxLayout()
        
        # Product search
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث عن منتج...")
        self.search_input.textChanged.connect(self.filter_products)
        search_layout.addWidget(self.search_input)
        
        # Categories
        self.category_combo = QComboBox()
        self.category_combo.addItem("الكل")
        self.category_combo.addItems(list(set(product['category'] for product in self.products)))
        self.category_combo.currentTextChanged.connect(self.filter_products)
        search_layout.addWidget(self.category_combo)
        
        left_panel.addLayout(search_layout)
        
        # Products grid
        self.products_grid = QWidget()
        self.products_layout = QVBoxLayout(self.products_grid)
        self.products_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        scroll_area = QWidget()
        scroll_area.setLayout(QVBoxLayout())
        scroll_area.layout().addWidget(self.products_grid)
        scroll_area.layout().setContentsMargins(0, 0, 0, 0)
        
        left_panel.addWidget(scroll_area)
        
        # Right side - Cart and checkout
        right_panel = QVBoxLayout()
        
        # Cart table
        self.cart_table = QTableWidget()
        self.cart_table.setColumnCount(5)
        self.cart_table.setHorizontalHeaderLabels(["المنتج", "السعر", "الكمية", "الإجمالي", ""])  # RTL
        self.cart_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        right_panel.addWidget(self.cart_table)
        
        # Total and buttons
        total_layout = QHBoxLayout()
        self.total_label = QLabel("الإجمالي: 0.00 ر.س")
        self.total_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        total_layout.addWidget(self.total_label)
        
        checkout_btn = QPushButton("إتمام البيع")
        checkout_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        checkout_btn.clicked.connect(self.checkout)
        total_layout.addWidget(checkout_btn)
        
        right_panel.addLayout(total_layout)
        
        # Add panels to main layout
        layout.addLayout(right_panel, stretch=2)
        layout.addLayout(left_panel, stretch=3)
        
        # Load products
        self.filter_products()
        
        # Apply built-in style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #4CAF50;
                color: white;
                padding: 8px;
                border: none;
            }
            QLineEdit, QComboBox, QDoubleSpinBox {
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
        """)
    
    def load_products(self):
        # Sample products - in a real app, this would load from a database
        self.products = [
            {"id": 1, "name": "كولا", "price": 5.0, "category": "مشروبات"},
            {"id": 2, "name": "شاي", "price": 2.0, "category": "مشروبات"},
            {"id": 3, "name": "قهوة", "price": 3.0, "category": "مشروبات"},
            {"id": 4, "name": "ساندويتش جبنة", "price": 10.0, "category": "وجبات"},
            {"id": 5, "name": "ساندويتش دجاج", "price": 12.0, "category": "وجبات"},
            {"id": 6, "name": "تشيز برجر", "price": 15.0, "category": "وجبات"},
            {"id": 7, "name": "بطاطس", "price": 5.0, "category": "مقبلات"},
            {"id": 8, "name":"سلطة", "price": 8.0, "category": "مقبلات"},
        ]
    
    def filter_products(self):
        # Clear existing products
        while self.products_layout.count():
            item = self.products_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        search_text = self.search_input.text().lower()
        category = self.category_combo.currentText()
        
        for product in self.products:
            if (search_text in product['name'].lower() or not search_text) and \
               (product['category'] == category or category == "الكل"):
                self.add_product_button(product)
    
    def add_product_button(self, product):
        btn = QPushButton(f"{product['name']}\n{product['price']} ر.س")
        btn.setMinimumHeight(80)
        btn.setStyleSheet("""
            QPushButton {
                text-align: center;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 14px;
                background-color: #f8f9fa;
            }
            QPushButton:hover {
                background-color: #e9ecef;
            }
        """)
        btn.clicked.connect(lambda checked, p=product: self.add_to_cart(p))
        self.products_layout.addWidget(btn)
    
    def add_to_cart(self, product):
        # Check if product already in cart
        for i, item in enumerate(self.cart):
            if item['id'] == product['id']:
                self.cart[i]['quantity'] += 1
                self.update_cart_display()
                return
        
        # Add new item to cart
        self.cart.append({
            'id': product['id'],
            'name': product['name'],
            'price': product['price'],
            'quantity': 1
        })
        self.update_cart_display()
    
    def update_cart_display(self):
        self.cart_table.setRowCount(len(self.cart))
        
        total = 0
        for row, item in enumerate(self.cart):
            # Product name
            self.cart_table.setItem(row, 0, QTableWidgetItem(item['name']))
            
            # Price
            self.cart_table.setItem(row, 1, QTableWidgetItem(f"{item['price']:.2f}"))
            
            # Quantity
            quantity_spin = QDoubleSpinBox()
            quantity_spin.setRange(1, 1000)
            quantity_spin.setValue(item['quantity'])
            quantity_spin.valueChanged.connect(lambda value, r=row: self.update_quantity(r, value))
            self.cart_table.setCellWidget(row, 2, quantity_spin)
            
            # Total
            item_total = item['price'] * item['quantity']
            self.cart_table.setItem(row, 3, QTableWidgetItem(f"{item_total:.2f}"))
            
            # Remove button
            remove_btn = QPushButton("حذف")
            remove_btn.setStyleSheet("background-color: #dc3545; color: white;")
            remove_btn.clicked.connect(lambda checked, r=row: self.remove_from_cart(r))
            self.cart_table.setCellWidget(row, 4, remove_btn)
            
            total += item_total
        
        self.total_label.setText(f"الإجمالي: {total:.2f} ر.س")
    
    def update_quantity(self, row, value):
        if 0 <= row < len(self.cart):
            self.cart[row]['quantity'] = int(value)
            self.update_cart_display()
    
    def remove_from_cart(self, row):
        if 0 <= row < len(self.cart):
            del self.cart[row]
            self.update_cart_display()
    
    def checkout(self):
        if not self.cart:
            QMessageBox.warning(self, "سلة فارغة", "لا توجد عناصر في السلة")
            return
        
        total = sum(item['price'] * item['quantity'] for item in self.cart)
        
        # In a real app, you would process payment here
        msg = QMessageBox()
        msg.setWindowTitle("إتمام البيع")
        msg.setText(f"تمت العملية بنجاح!\nالمبلغ الإجمالي: {total:.2f} ر.س")
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg.exec()
        
        # Clear cart after successful checkout
        self.cart = []
        self.update_cart_display()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set application style and font
    app.setStyle("Fusion")
    app.setFont(QFont("Arial", 10))
    
    # Set application direction to RTL for Arabic
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    window = POSApp()
    window.show()
    sys.exit(app.exec())
